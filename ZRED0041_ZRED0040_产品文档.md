# ZRED0041条款管理 & ZRED0040合同管理系统 - 产品文档

## 1. 系统概述

### 1.1 产品定位
ZRED0041条款管理系统与ZRED0040合同管理系统构成了完整的返利平台管理解决方案，实现了从合同创建到条款执行的全生命周期管理。

### 1.2 系统关系
- **ZRED0040合同管理**: 负责返利合同的基础信息管理、商品组配置
- **ZRED0041条款管理**: 负责具体返利条款的创建、修改、审批和执行
- **数据关联**: 通过合同编号(ZHT_ID)建立关联关系

## 2. 核心功能特性

### 2.1 ZRED0040 合同管理功能

#### 2.1.1 合同基本信息管理
- **合同类型配置**: 支持多种返利合同类型
- **业务伙伴管理**: 支持供应商(S)和经销商(M)两种类型
- **合同期间设置**: 灵活的开始和结束日期配置
- **组织架构**: 基于公司代码和采购组的权限控制
- **核算周期**: 支持月度、季度、半年度、年度核算

#### 2.1.2 商品组配置功能
- **商品范围定义**: 支持具体物料编码或ALL通配符
- **排除逻辑**: 支持商品排除标识配置
- **核算价格**: 支持分段核算价和价格倍数设置
- **促销配置**: 买赠促销规则设置

### 2.2 ZRED0041 条款管理功能

#### 2.2.1 条款创建与维护
- **多种创建模式**:
  - 全新创建: 从零开始创建条款
  - 参考创建: 基于现有条款创建
  - 次年预估: 基于历史条款创建预估条款
- **条款类型**: 支持普通条款和附加条款(P类型)
- **返利类型**: 数量返利、金额返利、比例返利、促销返利、固定返利、定额返利

#### 2.2.2 Excel批量导入功能
- **三种导入模式**:
  - 固定条款批量导入-创建(ZRED0004模板)
  - 固定条款批量导入-新增协议(ZRED0005模板)  
  - 条款批量修改(ZRED0007模板)
- **固定协议批量调整**: 支持预付返利调整(ZRED0006模板)

#### 2.2.3 审批流程管理
- **多级审批**: 支持条款审批和行项目审批
- **状态控制**: 
  - I: 初始状态
  - P: 审批中
  - A: 已审批
  - D: 已作废
- **权限控制**: 基于公司代码和活动类型的权限检查

## 3. 用户交互设计

### 3.1 选择屏幕交互

#### 3.1.1 ZRED0040 合同管理选择屏幕
- **合同类型选择**: 下拉列表选择合同类型
- **公司代码**: 支持搜索帮助的公司代码选择
- **操作模式**: 单选按钮组(创建/修改/显示)
- **次年预估**: 复选框控制次年预估功能

#### 3.1.2 ZRED0041 条款管理选择屏幕
- **返利类型**: 下拉列表选择返利类型
- **协议类型**: 下拉列表选择协议类型
- **合同编号**: 支持搜索帮助的合同选择
- **模板选择**: 组织模板下拉选择
- **批量处理**: 复选框控制Excel导入功能

### 3.2 主屏幕交互设计

#### 3.2.1 标签页结构
- **合同信息页签**: 基本信息维护
- **商品组页签**: 商品组列表和维护
- **返利条款页签**: 条款列表和管理

#### 3.2.2 表格控件交互
- **ALV表格**: 标准SAP ALV显示
- **行选择**: 支持单选和多选操作
- **工具栏**: 自定义工具栏按钮
- **双击操作**: 支持双击进入详细维护

### 3.3 按钮操作功能

#### 3.3.1 合同管理按钮
- **保存**: 保存合同基本信息
- **商品组维护**: 跳转到商品组维护界面
- **条款管理**: 跳转到条款管理界面
- **模板下载**: 下载Excel导入模板

#### 3.3.2 条款管理按钮
- **保存**: 保存条款信息
- **审批通过**: 条款审批操作
- **审批拒绝**: 条款拒绝操作
- **新增行项目**: 添加条款行项目
- **Excel导入**: 批量导入条款数据

## 4. Action操作校验细节

### 4.1 数据完整性校验

#### 4.1.1 必填字段校验
- **合同信息**: 合同描述、付款期间不能为空
- **条款信息**: 条款描述、付款期间必填
- **日期校验**: 开始日期不能大于结束日期
- **行项目校验**: 行项目号不能重复

#### 4.1.2 业务规则校验
- **促销返利阶梯**: 匹配比例合计不能大于100%
- **生效日期**: 必须在条款有效期范围内
- **月结锁定**: 不允许导入月结期前日期的条款

### 4.2 权限校验机制

#### 4.2.1 功能权限校验
```
权限对象: ZREAR009
字段1: BUKRS (公司代码)
字段2: ACTVT (活动类型: 01=创建, 02=修改, 03=显示)
```

#### 4.2.2 状态校验
- **审批中条款**: 不允许修改
- **已审批条款**: 不允许修改
- **已作废条款**: 不允许修改
- **次年预估条款**: 不能执行审批操作

### 4.3 Excel导入校验

#### 4.3.1 数据格式校验
- **必填字段**: 条款编码、公司代码、子类编码等
- **域值校验**: 专属标识、支付方式等字段域值检查
- **存在性校验**: 条款编码、收款方、物料编码等主数据存在性检查

#### 4.3.2 业务逻辑校验
- **重复性检查**: 协议ID重复性检查
- **关联性检查**: 条款与合同的关联关系检查
- **权限检查**: 导入数据的权限验证

## 5. 管理过程细节

### 5.1 合同创建流程
1. **选择合同类型和公司代码**
2. **填写合同基本信息** (描述、伙伴、期间等)
3. **配置商品组** (商品范围、核算价格)
4. **设置核算和结算周期**
5. **权限检查和数据验证**
6. **保存合同信息**

### 5.2 条款创建流程
1. **选择返利类型和协议类型**
2. **关联合同编号**
3. **填写条款基本信息**
4. **配置计算规则和商品组**
5. **设置生效期间和组织结构**
6. **添加附加条款(可选)**
7. **数据验证和权限检查**
8. **保存条款信息**

### 5.3 审批流程管理
1. **条款提交审批**
2. **权限检查** (审批权限验证)
3. **业务规则检查** (月结锁定、次年预估等)
4. **审批操作** (通过/拒绝)
5. **状态更新** (更新条款状态)
6. **消息通知** (审批结果通知)

### 5.4 Excel批量处理流程
1. **选择导入模式** (创建/修改/调整)
2. **下载对应模板**
3. **填写Excel数据**
4. **上传文件**
5. **数据格式校验**
6. **业务规则校验**
7. **权限检查**
8. **批量导入执行**
9. **结果反馈和错误处理**

## 6. 系统集成特性

### 6.1 程序间调用
- **ZRED0040调用ZRED0041**: 从合同管理跳转到条款管理
- **ZRED0041调用ZREM0002**: 条款管理调用商品组维护
- **ZRED0041调用ZRED0038**: 条款管理调用计算规则维护

### 6.2 内存参数传递
- **ZHT_ID**: 合同编号参数传递
- **ZTK_ID**: 条款编号参数传递
- **ZSPZ_ID**: 商品组编号参数传递

### 6.3 共用程序模块
- **ZRED0035_FPUB**: 返利公共功能模块
- **ZRED0040_M02/M03**: 合同和条款共用的屏幕模块
- **ZRED0035_F03**: 共用的功能模块

## 7. 错误处理和消息管理

### 7.1 统一消息处理机制
- **消息类型**: 成功(S)、警告(W)、错误(E)、信息(I)
- **消息显示**: 统一使用MESSAGE语句显示消息
- **消息列表**: 支持批量消息收集和显示

### 7.2 Excel导入错误处理
- **格式错误**: 数据格式不正确的处理
- **业务错误**: 业务规则校验失败的处理
- **权限错误**: 权限不足的错误提示
- **批量处理**: 支持部分成功、部分失败的处理模式

### 7.3 数据锁定机制
- **编辑锁定**: 防止多用户同时编辑同一条款
- **月结锁定**: 月结期间的数据保护机制
- **状态锁定**: 基于条款状态的编辑权限控制

## 8. 性能优化特性

### 8.1 数据查询优化
- **索引使用**: 基于主键和外键的高效查询
- **批量查询**: 使用FOR ALL ENTRIES优化关联查询
- **缓存机制**: 配置数据的内存缓存

### 8.2 用户体验优化
- **异步处理**: 大批量数据导入的异步处理
- **进度提示**: 长时间操作的进度显示
- **快捷操作**: 支持快捷键和工具栏操作

## 9. 扩展性设计

### 9.1 配置化管理
- **返利类型**: 可配置的返利类型扩展
- **协议类型**: 灵活的协议类型配置
- **计算规则**: 可扩展的计算规则框架

### 9.2 接口预留
- **外部系统接口**: 预留与其他系统的集成接口
- **报表接口**: 支持各种报表和分析需求
- **工作流接口**: 支持复杂审批流程的扩展

## 10. 安全性保障

### 10.1 数据安全
- **字段级权限**: 基于字段的访问控制
- **行级权限**: 基于数据行的访问控制
- **操作日志**: 完整的操作轨迹记录

### 10.2 业务安全
- **审批控制**: 严格的审批流程控制
- **状态管理**: 基于状态的操作权限控制
- **数据完整性**: 多层次的数据验证机制

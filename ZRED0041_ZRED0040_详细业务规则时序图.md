# ZRED0041条款管理 & ZRED0040合同管理系统 - 详细业务规则校验时序图

## 概述

本文档基于代码分析，详细展示了返利平台管理系统中具体的业务规则校验逻辑，将抽象的"业务规则检查"细化为具体的校验步骤和条件判断。

## 1. 合同创建详细业务规则校验时序图

### 校验规则说明
基于ZRED0040代码分析，合同创建涉及以下具体校验：
- 必填字段校验：合同描述、付款期间
- 日期逻辑校验：开始日期不能大于结束日期
- 分段核算价校验：起始日期和截止日期必填，起始日期需小于截止日期
- 数值格式校验：核算价、价格倍数、数量倍数必须为数字
- 促销数据校验：买数量、赠数量、加提单价必须为数字

### 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as ZRED0040界面
    participant Auth as 权限检查
    participant Valid as 数据验证
    participant DB as 数据库
    participant SPZ as 商品组管理

    User->>UI: 选择创建模式(ZRED0040A)
    UI->>Auth: 检查创建权限(ZREAR009)
    Auth->>Auth: 检查BUKRS字段权限
    Auth->>Auth: 检查ACTVT='01'权限
    Auth-->>UI: 权限验证结果
    
    alt 权限验证通过
        UI->>User: 显示合同创建界面
        User->>UI: 填写合同基本信息
        Note over User,UI: 合同类型、公司代码、伙伴信息等
        
        User->>UI: 配置商品组
        UI->>SPZ: 调用商品组维护(ZREM0002)
        
        alt 普通商品组配置
            SPZ->>SPZ: 校验物料编号格式
            SPZ->>SPZ: 校验核算价格为数字
            SPZ->>SPZ: 校验价格倍数为数字
            SPZ->>SPZ: 校验数量倍数为数字
            SPZ->>SPZ: 校验分段核算价日期
            SPZ->>SPZ: 检查起始日期<截止日期
            SPZ->>SPZ: 检查分段日期必填
        else 促销商品组配置
            SPZ->>SPZ: 校验物料组格式
            SPZ->>SPZ: 校验买数量为数字
            SPZ->>SPZ: 校验赠数量为数字
            SPZ->>SPZ: 校验加提单价为数字
        end
        
        SPZ->>DB: 保存商品组数据(ZRET0009/0020)
        SPZ-->>UI: 返回商品组ID
        
        User->>UI: 保存合同
        UI->>Valid: 开始数据完整性验证
        
        Valid->>Valid: 检查合同描述不为空
        Valid->>Valid: 检查付款期间不为空
        Valid->>Valid: 检查开始日期<=结束日期
        Valid->>Valid: 检查公司代码有效性
        Valid->>Valid: 检查业务伙伴代码有效性
        Valid->>Valid: 检查采购组有效性
        
        Valid->>DB: 检查合同编号唯一性
        DB-->>Valid: 返回检查结果
        
        alt 验证通过
            Valid->>DB: 生成合同编号
            DB-->>Valid: 返回合同编号
            Valid->>DB: 保存合同数据(ZRETA001)
            DB-->>Valid: 保存成功
            Valid->>UI: 返回成功消息
            UI->>User: 显示保存成功
        else 验证失败
            Valid->>UI: 返回具体错误消息
            UI->>User: 显示详细错误信息
        end
    else 权限验证失败
        Auth->>UI: 返回权限错误
        UI->>User: 显示无权限提示
    end
```

## 2. 条款创建详细业务规则校验时序图

### 校验规则说明
基于ZRED0041代码分析，条款创建涉及以下具体校验：
- 必填字段校验：条款描述、付款期间
- 日期范围校验：开始日期不能大于结束日期
- 截止日期检查：只允许创建指定日期之后的条款
- 促销返利阶梯校验：匹配比例合计不能大于100%
- 支付方状态校验：检查支付方是否被冻结或删除
- 行项目重复性校验：行项目号不能重复

### 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as ZRED0041界面
    participant Auth as 权限检查
    participant Valid as 数据验证
    participant DB as 数据库
    participant Rule as 计算规则
    participant Contract as 合同管理

    User->>UI: 选择条款创建(ZRED0041A)
    UI->>Auth: 检查创建权限
    Auth->>Auth: 检查BUKRS字段权限
    Auth->>Auth: 检查ACTVT='01'权限
    Auth-->>UI: 权限验证结果

    alt 权限验证通过
        UI->>User: 显示条款创建界面
        User->>UI: 选择返利类型和协议类型
        UI->>Contract: 获取合同信息
        Contract->>DB: 查询合同数据(ZRETA001)
        DB-->>Contract: 返回合同信息
        Contract-->>UI: 合同信息

        User->>UI: 填写条款基本信息
        Note over User,UI: 条款描述、生效期间、支付方式等

        alt 参考创建模式
            User->>UI: 选择参考条款
            UI->>DB: 查询参考条款数据
            DB-->>UI: 返回参考数据
            UI->>UI: 复制参考数据到新条款
        end

        User->>UI: 配置计算规则
        UI->>Rule: 调用规则维护(ZRED0038)
        Rule->>DB: 保存规则数据(ZRETC005)
        Rule-->>UI: 返回规则ID

        User->>UI: 关联商品组
        UI->>DB: 查询可用商品组
        DB-->>UI: 返回商品组列表

        User->>UI: 保存条款
        UI->>Valid: 开始条款数据验证

        Valid->>Valid: 检查条款描述不为空(frm_check_inital)
        Valid->>Valid: 检查付款期间不为空(frm_check_inital)
        Valid->>Valid: 检查开始日期<=结束日期

        Valid->>Valid: 执行截止日期检查(frm_check_zbegin)
        Valid->>DB: 查询用户截止日期配置(ZRET0053)
        DB-->>Valid: 返回截止日期限制
        Valid->>Valid: 检查条款开始日期>=截止日期

        loop 检查行项目数据
            Valid->>Valid: 检查行项目号不重复
            Valid->>Valid: 检查行描述不为空
            Valid->>Valid: 检查协议主体不为空
            Valid->>Valid: 检查收款方不为空

            Valid->>Valid: 执行支付方状态检查(frm_check_status_zflzff)
            Valid->>DB: 查询支付方主数据(LFA1)
            DB-->>Valid: 返回支付方状态
            Valid->>Valid: 检查支付方未被冻结(SPERR<>'X')
            Valid->>Valid: 检查支付方未被删除(LOEVM<>'X')

            Valid->>Valid: 执行支付方公司代码检查(frm_check_status_zflzff_bukrs)
            Valid->>DB: 查询支付方公司代码数据(LFB1)
            DB-->>Valid: 返回公司代码级支付方状态
            Valid->>Valid: 检查公司代码级支付方状态
        end

        alt 促销返利类型(zxybstyp='P')
            Valid->>Valid: 执行促销返利阶梯检查
            Valid->>Valid: 计算匹配比例合计(SUM zcpctg)
            Valid->>Valid: 检查合计<=100%
        end

        Valid->>Valid: 执行生效日期范围检查(frm_check_zdates)
        Valid->>Valid: 检查生效日期在条款有效期内

        alt 验证通过
            Valid->>DB: 生成条款编号(公司代码+年度+返利类型+序号)
            DB-->>Valid: 返回条款编号
            Valid->>DB: 保存条款数据(ZRETA002)
            Valid->>DB: 保存关联数据(ZRET0006)
            DB-->>Valid: 保存成功
            Valid->>UI: 返回成功消息
            UI->>User: 显示保存成功
        else 验证失败
            Valid->>UI: 返回具体错误消息列表
            UI->>User: 显示详细错误信息
        end
    else 权限验证失败
        Auth->>UI: 返回权限错误
        UI->>User: 显示无权限提示
    end
```

-- =====================================================
-- ZRED0040 返利合同管理系统 - MySQL数据表结构
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS rebate_contract_system 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE rebate_contract_system;

-- =====================================================
-- 1. 主业务表
-- =====================================================

-- 1.1 返利合同主表
CREATE TABLE zreta001 (
    zht_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    zhtlx VARCHAR(4) COMMENT '合同类型',
    zbukrs VARCHAR(4) COMMENT '公司代码',
    zht_txt VARCHAR(40) COMMENT '合同描述',
    zhtid VARCHAR(35) COMMENT '关联合同号',
    zbptype VARCHAR(1) COMMENT '伙伴类型(S=供应商,M=经销商)',
    zbpcode VARCHAR(10) COMMENT '伙伴代码',
    ekgrp VARCHAR(3) COMMENT '采购组',
    zhtyear VARCHAR(4) COMMENT '签署年度',
    zbegin DATE COMMENT '开始日期',
    zend DATE COMMENT '结束日期',
    ztmpid VARCHAR(10) COMMENT '组织模板',
    zhstype VARCHAR(1) COMMENT '核算类型(A=年度,B=期间)',
    zhszq VARCHAR(3) COMMENT '核算周期',
    zjszq VARCHAR(3) COMMENT '结算周期',
    zdffs VARCHAR(1) COMMENT '兑付方式(O=线下,N=线上)',
    zflzff VARCHAR(10) COMMENT '支付方',
    zpayday VARCHAR(2) COMMENT '付款期间',
    zlxr VARCHAR(30) COMMENT '联系人',
    zlxfs VARCHAR(30) COMMENT '联系方式',
    zcnyght_id VARCHAR(20) COMMENT '次年预估合同ID',
    zcnygsg VARCHAR(1) COMMENT '次年预估标识',
    zcjr VARCHAR(12) COMMENT '创建人',
    zcjrq DATE COMMENT '创建日期',
    zcjsj TIME COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',
    PRIMARY KEY (zht_id),
    INDEX idx_zbukrs_zhtlx (zbukrs, zhtlx),
    INDEX idx_zbpcode (zbpcode),
    INDEX idx_zbegin_zend (zbegin, zend),
    INDEX idx_zcjrq (zcjrq)
) ENGINE=InnoDB COMMENT='返利合同主表';

-- 1.2 返利条款表
CREATE TABLE zreta002 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zht_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    zfllx VARCHAR(4) COMMENT '返利类型',
    ztktype VARCHAR(1) COMMENT '条款类型(空=普通,P=附加)',
    ztk_txt VARCHAR(40) COMMENT '条款描述',
    zspz_id VARCHAR(20) COMMENT '商品组编号',
    zflspz_id VARCHAR(20) COMMENT '返利商品组编号',
    zbegin DATE COMMENT '开始日期',
    zend DATE COMMENT '结束日期',
    ztmpid VARCHAR(10) COMMENT '组织模板',
    zxyzt VARCHAR(1) COMMENT '协议状态(A=活跃,I=非活跃)',
    zclrid VARCHAR(10) COMMENT '计算规则ID',
    zleib VARCHAR(1) COMMENT '类别(R=授权类)',
    zxybstyp VARCHAR(1) COMMENT '协议类型(A=数量,T=金额,V=比例,P=促销,F=固定,Q=定额)',
    zcjr VARCHAR(12) COMMENT '创建人',
    zcjrq DATE COMMENT '创建日期',
    zcjsj TIME COMMENT '创建时间',
    PRIMARY KEY (ztk_id),
    FOREIGN KEY (zht_id) REFERENCES zreta001(zht_id) ON DELETE CASCADE,
    INDEX idx_zht_id (zht_id),
    INDEX idx_zfllx (zfllx),
    INDEX idx_zspz_id (zspz_id),
    INDEX idx_zxyzt (zxyzt)
) ENGINE=InnoDB COMMENT='返利条款表';

-- 1.3 商品组主表
CREATE TABLE zret0009 (
    zspz_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    zht_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    zspzid_txt VARCHAR(40) COMMENT '商品组描述',
    zusage VARCHAR(1) COMMENT '用途(P=促销,空=普通)',
    zbpcode VARCHAR(10) COMMENT '业务伙伴代码',
    zcjr VARCHAR(12) COMMENT '创建人',
    zcjrq DATE COMMENT '创建日期',
    zcjsj TIME COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',
    PRIMARY KEY (zspz_id),
    FOREIGN KEY (zht_id) REFERENCES zreta001(zht_id) ON DELETE CASCADE,
    INDEX idx_zht_id (zht_id),
    INDEX idx_zusage (zusage),
    INDEX idx_zbpcode (zbpcode)
) ENGINE=InnoDB COMMENT='商品组主表';

-- 1.4 商品组明细表
CREATE TABLE zret0020 (
    zspz_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    matnr VARCHAR(18) NOT NULL COMMENT '物料编号',
    zmgroup VARCHAR(10) NOT NULL DEFAULT '' COMMENT '物料组',
    zht_id VARCHAR(20) COMMENT '合同编号',
    zacpr DECIMAL(15,2) COMMENT '核算价格',
    zpeinh DECIMAL(5,0) DEFAULT 1 COMMENT '价格倍数',
    zpeinh_q DECIMAL(5,0) DEFAULT 1 COMMENT '数量倍数',
    zspbc VARCHAR(1) COMMENT '排除标识(X=排除)',
    zbuy DECIMAL(13,3) COMMENT '买数量(促销用)',
    zfree DECIMAL(13,3) COMMENT '赠数量(促销用)',
    zcmpst DECIMAL(15,2) COMMENT '加提单价(促销用)',
    PRIMARY KEY (zspz_id, matnr, zmgroup),
    FOREIGN KEY (zspz_id) REFERENCES zret0009(zspz_id) ON DELETE CASCADE,
    INDEX idx_matnr (matnr),
    INDEX idx_zht_id (zht_id),
    INDEX idx_zspbc (zspbc)
) ENGINE=InnoDB COMMENT='商品组明细表';

-- 1.5 分段核算价表
CREATE TABLE zret0020_item (
    zspz_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    matnr VARCHAR(18) NOT NULL COMMENT '物料编号',
    zmgroup VARCHAR(10) NOT NULL COMMENT '物料组',
    zbegdt DATE NOT NULL COMMENT '开始日期',
    zenddt DATE COMMENT '结束日期',
    zacpr DECIMAL(15,2) COMMENT '核算价格',
    PRIMARY KEY (zspz_id, matnr, zmgroup, zbegdt),
    FOREIGN KEY (zspz_id, matnr, zmgroup) REFERENCES zret0020(zspz_id, matnr, zmgroup) ON DELETE CASCADE,
    INDEX idx_date_range (zbegdt, zenddt)
) ENGINE=InnoDB COMMENT='分段核算价表';

-- =====================================================
-- 2. 配置表
-- =====================================================

-- 2.1 组织模板配置表
CREATE TABLE zretc001 (
    ztmpid VARCHAR(10) NOT NULL COMMENT '模板编号',
    zhtlx VARCHAR(4) NOT NULL COMMENT '合同类型',
    zbukrs VARCHAR(4) COMMENT '公司代码',
    ztmptxt VARCHAR(40) COMMENT '模板描述',
    ztktype VARCHAR(1) COMMENT '条款类型(空=普通,P=附加)',
    ztmpty VARCHAR(1) COMMENT '模板类型(S=标准)',
    PRIMARY KEY (ztmpid, zhtlx),
    INDEX idx_zbukrs (zbukrs),
    INDEX idx_ztktype (ztktype)
) ENGINE=InnoDB COMMENT='组织模板配置表';

-- 2.2 协议类型配置表
CREATE TABLE zretc009 (
    zxybstyp VARCHAR(1) NOT NULL COMMENT '协议类型',
    zfllx VARCHAR(4) NOT NULL COMMENT '返利类型',
    PRIMARY KEY (zxybstyp, zfllx)
) ENGINE=InnoDB COMMENT='协议类型配置表';

-- 2.3 返利类型配置表
CREATE TABLE zret0002 (
    zfllx VARCHAR(4) NOT NULL COMMENT '返利类型代码',
    zfllx_txt VARCHAR(40) COMMENT '返利类型描述',
    PRIMARY KEY (zfllx)
) ENGINE=InnoDB COMMENT='返利类型配置表';

-- 2.4 核算周期配置表
CREATE TABLE zret0005 (
    zhszq VARCHAR(3) NOT NULL COMMENT '核算周期代码',
    zhszq_txt VARCHAR(40) COMMENT '核算周期描述',
    PRIMARY KEY (zhszq)
) ENGINE=InnoDB COMMENT='核算周期配置表';

-- 2.5 结算周期配置表
CREATE TABLE zret0004 (
    zjszq VARCHAR(3) NOT NULL COMMENT '结算周期代码',
    zjszq_txt VARCHAR(40) COMMENT '结算周期描述',
    PRIMARY KEY (zjszq)
) ENGINE=InnoDB COMMENT='结算周期配置表';

-- 2.6 核算基准配置表
CREATE TABLE zret0003 (
    zhsjz VARCHAR(3) NOT NULL COMMENT '核算基准代码',
    zhsjz_txt VARCHAR(40) COMMENT '核算基准描述',
    zlower VARCHAR(1) COMMENT '取最小值标识',
    zhigher VARCHAR(1) COMMENT '取最大值标识',
    zpur VARCHAR(1) COMMENT '采购标识',
    zdist VARCHAR(1) COMMENT '分销标识',
    zsale VARCHAR(1) COMMENT '销售标识',
    zduepay VARCHAR(1) COMMENT '应付标识',
    zactlpay VARCHAR(1) COMMENT '实付标识',
    PRIMARY KEY (zhsjz)
) ENGINE=InnoDB COMMENT='核算基准配置表';

-- 2.7 计算规则配置表
CREATE TABLE zretc005 (
    zclrid VARCHAR(10) NOT NULL COMMENT '计算规则ID',
    zhsjz VARCHAR(3) COMMENT '核算基准',
    zflxs VARCHAR(1) COMMENT '返利形式(M=货币)',
    PRIMARY KEY (zclrid),
    FOREIGN KEY (zhsjz) REFERENCES zret0003(zhsjz)
) ENGINE=InnoDB COMMENT='计算规则配置表';

-- =====================================================
-- 3. 业务数据表
-- =====================================================

-- 3.1 返利协议关联表
CREATE TABLE zret0006 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款ID',
    zbukrs VARCHAR(4) COMMENT '公司代码',
    PRIMARY KEY (zxy_id, ztk_id),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id),
    INDEX idx_zbukrs (zbukrs)
) ENGINE=InnoDB COMMENT='返利协议关联表';

-- 3.2 返利计算结果表
CREATE TABLE zret0017 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    zpurysje DECIMAL(15,2) COMMENT '采购预算金额',
    zdistysje DECIMAL(15,2) COMMENT '分销预算金额',
    zsaleysje DECIMAL(15,2) COMMENT '销售预算金额',
    zduercv DECIMAL(15,2) COMMENT '应收金额',
    zactlrcv DECIMAL(15,2) COMMENT '实收金额',
    PRIMARY KEY (zxy_id)
) ENGINE=InnoDB COMMENT='返利计算结果表';

-- =====================================================
-- 4. 日志表
-- =====================================================

-- 4.1 状态变更日志表
CREATE TABLE zret0050 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款ID',
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    zxyzt_o VARCHAR(1) COMMENT '原状态',
    zxyzt_n VARCHAR(1) COMMENT '新状态',
    frgc1_o VARCHAR(2) COMMENT '原审批代码',
    frgc1_n VARCHAR(2) COMMENT '新审批代码',
    zcjrq DATE COMMENT '操作日期',
    zcjsj TIME COMMENT '操作时间',
    zcjr VARCHAR(12) COMMENT '操作人',
    PRIMARY KEY (ztk_id, zxy_id, zcjrq, zcjsj),
    INDEX idx_zcjrq (zcjrq)
) ENGINE=InnoDB COMMENT='状态变更日志表';

-- 4.2 修改说明日志表
CREATE TABLE zret0076 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款ID',
    zxyzt VARCHAR(1) COMMENT '状态',
    ztext1 VARCHAR(120) COMMENT '修改说明1',
    ztext2 VARCHAR(120) COMMENT '修改说明2',
    ztext3 VARCHAR(120) COMMENT '修改说明3',
    ztext4 VARCHAR(120) COMMENT '修改说明4',
    erdat DATE COMMENT '建立日期',
    ertim TIME COMMENT '创建时间',
    ernam VARCHAR(12) COMMENT '创建人',
    PRIMARY KEY (ztk_id, erdat, ertim),
    INDEX idx_erdat (erdat)
) ENGINE=InnoDB COMMENT='修改说明日志表';

-- =====================================================
-- 5. 初始化数据
-- =====================================================

-- 插入返利类型基础数据
INSERT INTO zret0002 (zfllx, zfllx_txt) VALUES
('RB01', '数量返利'),
('RB02', '固定返利'),
('RB03', '定额返利'),
('RB04', '阶梯返利'),
('RB05', '金额返利'),
('RB06', '比例返利');

-- 插入核算周期基础数据
INSERT INTO zret0005 (zhszq, zhszq_txt) VALUES
('1M', '月度'),
('1Q', '季度'),
('1H', '半年度'),
('1Y', '年度');

-- 插入结算周期基础数据
INSERT INTO zret0004 (zjszq, zjszq_txt) VALUES
('1M', '月结'),
('3M', '季结'),
('6M', '半年结'),
('12M', '年结');

-- 插入核算基准基础数据
INSERT INTO zret0003 (zhsjz, zhsjz_txt, zlower, zhigher, zpur, zdist, zsale, zduepay, zactlpay) VALUES
('MIN', '取最小值', 'X', '', '', '', '', '', ''),
('MAX', '取最大值', '', 'X', '', '', '', '', ''),
('PUR', '采购金额', '', '', 'X', '', '', '', ''),
('DIS', '分销金额', '', '', '', 'X', '', '', ''),
('SAL', '销售金额', '', '', '', '', 'X', '', ''),
('DUE', '应付金额', '', '', '', '', '', 'X', ''),
('ACT', '实付金额', '', '', '', '', '', '', 'X');

-- =====================================================
-- 6. 视图定义
-- =====================================================

-- 合同汇总视图
CREATE VIEW v_contract_summary AS
SELECT 
    a.zht_id,
    a.zht_txt,
    a.zhtlx,
    a.zbukrs,
    a.zbpcode,
    a.zbegin,
    a.zend,
    COUNT(DISTINCT b.ztk_id) AS clause_count,
    COUNT(DISTINCT c.zspz_id) AS product_group_count
FROM zreta001 a
LEFT JOIN zreta002 b ON a.zht_id = b.zht_id
LEFT JOIN zret0009 c ON a.zht_id = c.zht_id
GROUP BY a.zht_id, a.zht_txt, a.zhtlx, a.zbukrs, a.zbpcode, a.zbegin, a.zend;

-- 商品组明细视图
CREATE VIEW v_product_group_detail AS
SELECT 
    a.zspz_id,
    a.zspzid_txt,
    a.zusage,
    b.matnr,
    b.zmgroup,
    b.zacpr,
    b.zpeinh,
    b.zpeinh_q,
    b.zspbc,
    CASE WHEN b.zspbc = 'X' THEN '排除' ELSE '包含' END AS exclude_flag_text
FROM zret0009 a
INNER JOIN zret0020 b ON a.zspz_id = b.zspz_id;

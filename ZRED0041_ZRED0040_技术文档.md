# ZRED0041条款管理 & ZRED0040合同管理系统 - 技术文档

## 1. 系统架构概述

### 1.1 程序结构关系
```
ZRED0040 (合同管理主程序)
├── ZRED0040_T01 (类型定义)
├── ZRED0040_T02 (商品组类型定义)  
├── ZRED0040_S01 (选择屏幕)
├── ZRED0040_M01/M02/M03 (屏幕模块)
├── ZRED0040_F01/F02 (功能模块)
└── ZRED0035_FPUB (公共功能)

ZRED0041 (条款管理主程序)
├── ZRED0041_T01/T03 (类型定义)
├── ZRED0041_S01 (选择屏幕)
├── ZRED0041_M01/M02 (屏幕模块)
├── ZRED0041_F01~F07 (功能模块)
├── ZRED0041_C01 (ALV类定义)
├── 共用模块 (ZRED0040_M02/M03, ZRED0035_*)
└── ZRED0038_F02 (规则应用模块)
```

### 1.2 事务代码体系
- **ZRED0040A/B/C**: 合同创建/修改/显示
- **ZRED0041A/B/C/D/E/F**: 条款创建/修改/显示/审批/新增行项目/批量调整

## 2. MySQL数据库表结构设计

### 2.1 核心业务表

#### 2.1.1 返利合同主表 (zreta001)
```sql
CREATE TABLE zreta001 (
    zht_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    zhtlx VARCHAR(4) COMMENT '合同类型',
    zbukrs VARCHAR(4) COMMENT '公司代码',
    zht_txt VARCHAR(40) COMMENT '合同描述',
    zhtid VARCHAR(35) COMMENT '关联合同号',
    zbptype VARCHAR(1) COMMENT '伙伴类型(S=供应商,M=经销商)',
    zbpcode VARCHAR(10) COMMENT '伙伴代码',
    ekgrp VARCHAR(3) COMMENT '采购组',
    zhtyear VARCHAR(4) COMMENT '签署年度',
    zbegin DATE COMMENT '开始日期',
    zend DATE COMMENT '结束日期',
    ztmpid VARCHAR(10) COMMENT '组织模板',
    zhstype VARCHAR(1) COMMENT '核算类型(A=年度,B=期间)',
    zhszq VARCHAR(3) COMMENT '核算周期',
    zjszq VARCHAR(3) COMMENT '结算周期',
    zdffs VARCHAR(1) COMMENT '兑付方式(O=线下,N=线上)',
    zflzff VARCHAR(10) COMMENT '支付方',
    zpayday VARCHAR(2) COMMENT '付款期间',
    zlxr VARCHAR(30) COMMENT '联系人',
    zlxfs VARCHAR(30) COMMENT '联系方式',
    zcjr VARCHAR(12) COMMENT '创建人',
    zcjrq DATE COMMENT '创建日期',
    zcjsj TIME COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',
    PRIMARY KEY (zht_id),
    INDEX idx_zbukrs (zbukrs),
    INDEX idx_zbpcode (zbpcode),
    INDEX idx_ekgrp (ekgrp),
    INDEX idx_zhtyear (zhtyear),
    INDEX idx_date_range (zbegin, zend)
) ENGINE=InnoDB COMMENT='返利合同主表';
```

#### 2.1.2 返利条款表 (zreta002)
```sql
CREATE TABLE zreta002 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zht_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    zfllx VARCHAR(4) COMMENT '返利类型',
    ztktype VARCHAR(1) COMMENT '条款类型(空=普通,P=附加)',
    ztk_txt VARCHAR(40) COMMENT '条款描述',
    zspz_id VARCHAR(20) COMMENT '商品组编号',
    zflspz_id VARCHAR(20) COMMENT '返利商品组编号',
    zbegin DATE COMMENT '开始日期',
    zend DATE COMMENT '结束日期',
    ztmpid VARCHAR(10) COMMENT '组织模板',
    zxyzt VARCHAR(1) COMMENT '协议状态(I=初始,P=审批中,A=已审批,D=已作废)',
    zclrid VARCHAR(10) COMMENT '计算规则ID',
    zleib VARCHAR(1) COMMENT '类别(R=授权类)',
    zxybstyp VARCHAR(1) COMMENT '协议类型(A=数量,T=金额,V=比例,P=促销,F=固定,Q=定额)',
    zhstype VARCHAR(1) COMMENT '核算类型',
    zhszq VARCHAR(3) COMMENT '核算周期',
    zjszq VARCHAR(3) COMMENT '结算周期',
    zpayday VARCHAR(2) COMMENT '付款期间',
    zhscj VARCHAR(10) COMMENT '核算厂家',
    zflzff VARCHAR(10) COMMENT '支付方',
    zcnyg VARCHAR(1) COMMENT '次年预估标识',
    zdybm VARCHAR(20) COMMENT '对应编码',
    frgc1 VARCHAR(2) COMMENT '审批代码',
    zcjr VARCHAR(12) COMMENT '创建人',
    zcjrq DATE COMMENT '创建日期',
    zcjsj TIME COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',
    PRIMARY KEY (ztk_id),
    FOREIGN KEY (zht_id) REFERENCES zreta001(zht_id) ON DELETE CASCADE,
    INDEX idx_zht_id (zht_id),
    INDEX idx_zfllx (zfllx),
    INDEX idx_zxyzt (zxyzt),
    INDEX idx_zxybstyp (zxybstyp),
    INDEX idx_date_range (zbegin, zend),
    INDEX idx_zcjrq (zcjrq)
) ENGINE=InnoDB COMMENT='返利条款表';
```

#### 2.1.3 商品组主表 (zret0009)
```sql
CREATE TABLE zret0009 (
    zspz_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    zht_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    zspzid_txt VARCHAR(40) COMMENT '商品组描述',
    zusage VARCHAR(1) COMMENT '用途(P=促销,空=普通)',
    zbpcode VARCHAR(10) COMMENT '业务伙伴代码',
    zcjr VARCHAR(12) COMMENT '创建人',
    zcjrq DATE COMMENT '创建日期',
    zcjsj TIME COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',
    PRIMARY KEY (zspz_id),
    FOREIGN KEY (zht_id) REFERENCES zreta001(zht_id) ON DELETE CASCADE,
    INDEX idx_zht_id (zht_id),
    INDEX idx_zusage (zusage),
    INDEX idx_zbpcode (zbpcode)
) ENGINE=InnoDB COMMENT='商品组主表';
```

#### 2.1.4 商品组明细表 (zret0020)
```sql
CREATE TABLE zret0020 (
    zspz_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    matnr VARCHAR(18) NOT NULL COMMENT '物料编号',
    zmgroup VARCHAR(10) NOT NULL DEFAULT '' COMMENT '物料组',
    zht_id VARCHAR(20) COMMENT '合同编号',
    zacpr DECIMAL(15,2) COMMENT '核算价格',
    zpeinh DECIMAL(5,0) DEFAULT 1 COMMENT '价格倍数',
    zpeinh_q DECIMAL(5,0) DEFAULT 1 COMMENT '数量倍数',
    zspbc VARCHAR(1) COMMENT '排除标识(X=排除)',
    zbuy DECIMAL(13,3) COMMENT '买数量(促销用)',
    zfree DECIMAL(13,3) COMMENT '赠数量(促销用)',
    zcmpst DECIMAL(15,2) COMMENT '加提单价(促销用)',
    PRIMARY KEY (zspz_id, matnr, zmgroup),
    FOREIGN KEY (zspz_id) REFERENCES zret0009(zspz_id) ON DELETE CASCADE,
    INDEX idx_matnr (matnr),
    INDEX idx_zht_id (zht_id),
    INDEX idx_zspbc (zspbc)
) ENGINE=InnoDB COMMENT='商品组明细表';
```

#### 2.1.5 分段核算价表 (zret0020_item)
```sql
CREATE TABLE zret0020_item (
    zspz_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    matnr VARCHAR(18) NOT NULL COMMENT '物料编号',
    zmgroup VARCHAR(10) NOT NULL COMMENT '物料组',
    zbegdt DATE NOT NULL COMMENT '开始日期',
    zenddt DATE COMMENT '结束日期',
    zacpr DECIMAL(15,2) COMMENT '核算价格',
    PRIMARY KEY (zspz_id, matnr, zmgroup, zbegdt),
    FOREIGN KEY (zspz_id, matnr, zmgroup) REFERENCES zret0020(zspz_id, matnr, zmgroup) ON DELETE CASCADE,
    INDEX idx_date_range (zbegdt, zenddt)
) ENGINE=InnoDB COMMENT='分段核算价表';
```

### 2.2 协议相关表

#### 2.2.1 返利协议关联表 (zret0006)
```sql
CREATE TABLE zret0006 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款ID',
    zbukrs VARCHAR(4) COMMENT '公司代码',
    PRIMARY KEY (zxy_id, ztk_id),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id),
    INDEX idx_zbukrs (zbukrs)
) ENGINE=InnoDB COMMENT='返利协议关联表';
```

#### 2.2.2 固定协议行项目表 (zret0008)
```sql
CREATE TABLE zret0008 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zbukrs VARCHAR(4) COMMENT '公司代码',
    werks VARCHAR(4) COMMENT '工厂',
    matnr VARCHAR(18) COMMENT '物料编号',
    zsl DECIMAL(13,3) COMMENT '数量',
    zje DECIMAL(15,2) COMMENT '金额',
    zpaytp VARCHAR(1) COMMENT '支付类型',
    zflzff VARCHAR(10) COMMENT '支付方',
    zdate DATE COMMENT '生效日期',
    zmwskz VARCHAR(1) COMMENT '税码',
    zflbz VARCHAR(1) COMMENT '返利标识',
    zfljs VARCHAR(1) COMMENT '返利结算',
    zdffs VARCHAR(1) COMMENT '兑付方式',
    zzsbs VARCHAR(1) COMMENT '专属标识',
    zctgr VARCHAR(4) COMMENT '类别组',
    zzlbm VARCHAR(10) COMMENT '子类编码',
    zqdbm VARCHAR(10) COMMENT '渠道编码',
    zjzzd VARCHAR(1) COMMENT '集中制单',
    zsqbm_xy VARCHAR(20) COMMENT '申请编码',
    PRIMARY KEY (zxy_id, zitems),
    FOREIGN KEY (zxy_id) REFERENCES zret0006(zxy_id) ON DELETE CASCADE,
    INDEX idx_zbukrs (zbukrs),
    INDEX idx_werks (werks),
    INDEX idx_matnr (matnr),
    INDEX idx_zdate (zdate)
) ENGINE=InnoDB COMMENT='固定协议行项目表';
```

### 2.3 配置表

#### 2.3.1 组织模板配置表 (zretc001)
```sql
CREATE TABLE zretc001 (
    ztmpid VARCHAR(10) NOT NULL COMMENT '模板编号',
    zhtlx VARCHAR(4) NOT NULL COMMENT '合同类型',
    zbukrs VARCHAR(4) COMMENT '公司代码',
    ztmptxt VARCHAR(40) COMMENT '模板描述',
    ztktype VARCHAR(1) COMMENT '条款类型(空=普通,P=附加)',
    ztmpty VARCHAR(1) COMMENT '模板类型(S=标准)',
    PRIMARY KEY (ztmpid, zhtlx),
    INDEX idx_zbukrs (zbukrs),
    INDEX idx_ztktype (ztktype)
) ENGINE=InnoDB COMMENT='组织模板配置表';
```

#### 2.3.2 协议类型配置表 (zretc009)
```sql
CREATE TABLE zretc009 (
    zxybstyp VARCHAR(1) NOT NULL COMMENT '协议类型',
    zfllx VARCHAR(4) NOT NULL COMMENT '返利类型',
    zlrsi VARCHAR(1) COMMENT '允许新增行项目标识',
    PRIMARY KEY (zxybstyp, zfllx)
) ENGINE=InnoDB COMMENT='协议类型配置表';
```

#### 2.3.3 返利类型配置表 (zret0002)
```sql
CREATE TABLE zret0002 (
    zfllx VARCHAR(4) NOT NULL COMMENT '返利类型代码',
    zfllx_txt VARCHAR(40) COMMENT '返利类型描述',
    PRIMARY KEY (zfllx)
) ENGINE=InnoDB COMMENT='返利类型配置表';
```

#### 2.3.4 计算规则配置表 (zretc005)
```sql
CREATE TABLE zretc005 (
    zclrid VARCHAR(10) NOT NULL COMMENT '计算规则ID',
    zhsjz VARCHAR(3) COMMENT '核算基准',
    zflxs VARCHAR(1) COMMENT '返利形式(M=货币)',
    zclrtp VARCHAR(1) COMMENT '规则类型(S=标准,M=手工)',
    PRIMARY KEY (zclrid)
) ENGINE=InnoDB COMMENT='计算规则配置表';
```

### 2.4 审批和日志表

#### 2.4.1 条款修改说明表 (zret0076)
```sql
CREATE TABLE zret0076 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款ID',
    zseq VARCHAR(6) NOT NULL COMMENT '序号',
    ztext VARCHAR(255) COMMENT '修改说明文本',
    zcjr VARCHAR(12) COMMENT '创建人',
    zcjrq DATE COMMENT '创建日期',
    zcjsj TIME COMMENT '创建时间',
    PRIMARY KEY (ztk_id, zseq),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='条款修改说明表';
```

#### 2.4.2 状态变更日志表 (zret0050)
```sql
CREATE TABLE zret0050 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款ID',
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    zxyzt_o VARCHAR(1) COMMENT '原状态',
    zxyzt_n VARCHAR(1) COMMENT '新状态',
    frgc1_o VARCHAR(2) COMMENT '原审批代码',
    frgc1_n VARCHAR(2) COMMENT '新审批代码',
    zcjrq DATE COMMENT '操作日期',
    zcjsj TIME COMMENT '操作时间',
    zcjr VARCHAR(12) COMMENT '操作人',
    zmemo VARCHAR(255) COMMENT '备注',
    PRIMARY KEY (ztk_id, zxy_id, zcjrq, zcjsj),
    INDEX idx_zcjrq (zcjrq),
    INDEX idx_zcjr (zcjr)
) ENGINE=InnoDB COMMENT='状态变更日志表';
```

## 3. 核心业务流程时序图

### 3.1 合同创建流程
上面的Mermaid图展示了ZRED0040合同创建的完整流程，包括权限检查、数据验证、商品组配置等关键步骤。

### 3.2 条款创建流程
条款创建流程展示了ZRED0041中条款从创建到保存的完整过程，包括参考创建、规则配置、商品组关联等功能。

### 3.3 条款审批流程
审批流程时序图详细展示了条款审批的业务逻辑，包括锁定机制、状态变更、日志记录等关键环节。

### 3.4 Excel批量导入流程
批量导入流程展示了从文件上传到数据保存的完整处理链路，包括数据验证、权限检查、错误处理等机制。

## 4. 数据关系图

上面的ER图展示了系统核心表之间的关系，体现了从合同到条款、从商品组到明细的完整数据结构。

## 5. 核心算法和业务规则

### 5.1 条款编号生成算法
```sql
-- 条款编号生成规则: 公司代码 + 年度 + 返利类型 + 6位序号
-- 示例: 1000202400010001
SELECT CONCAT(
    zbukrs,                    -- 公司代码 (4位)
    YEAR(CURDATE()),          -- 年度 (4位)
    zfllx,                    -- 返利类型 (4位)
    LPAD(next_seq, 6, '0')    -- 序号 (6位)
) AS ztk_id;
```

### 5.2 权限检查算法
```abap
" 权限检查逻辑
AUTHORITY-CHECK OBJECT 'ZREAR009'
  ID 'BUKRS' FIELD ls_data-zbukrs
  ID 'ACTVT' FIELD lv_actvt.

IF sy-subrc NE 0.
  " 权限不足处理
  MESSAGE e001(zre) WITH '无权限操作该公司代码的数据'.
ENDIF.
```

### 5.3 状态转换规则
```
状态转换矩阵:
I (初始) -> P (审批中) : 提交审批
P (审批中) -> A (已审批) : 审批通过
P (审批中) -> I (初始) : 审批拒绝
A (已审批) -> D (已作废) : 作废操作
```

### 5.4 月结锁定控制
```abap
" 月结锁定期间检查
PERFORM frm_get_zlock CHANGING lv_zlock.
PERFORM frm_get_lasdy CHANGING lv_sylsd.

IF lv_zlock = 'X' AND ls_data-zbegin <= lv_sylsd.
  MESSAGE e002(zre) WITH '月结锁定期间不允许操作'.
ENDIF.
```

## 6. 接口设计

### 6.1 程序间调用接口
```abap
" ZRED0040调用ZRED0041
SUBMIT zred0041
  WITH p_zfllx = gs_tk_call-zfllx
  WITH p_zxybtp = gs_tk_call-zxybstyp
  WITH p_zht_id = gs_ta01-zht_id
  AND RETURN.

" ZRED0041调用ZREM0002
SUBMIT zrem0002
  WITH rb_add = 'X'
  WITH p_zht_id = gs_ta01-zht_id
  WITH p_zusage = lv_zusage
  AND RETURN.
```

### 6.2 内存参数传递
```abap
" 设置内存参数
SET PARAMETER ID 'ZHT_ID' FIELD gs_ta01-zht_id.
SET PARAMETER ID 'ZTK_ID' FIELD gs_ta02-ztk_id.

" 获取内存参数
GET PARAMETER ID 'ZHT_ID' FIELD p_zht_id.
GET PARAMETER ID 'ZTK_ID' FIELD p_ztk_id.
```

### 6.3 ALV事件处理
```abap
" ALV工具栏事件
METHOD sec_handle_toolbar.
  PERFORM handle_toolbar USING e_object e_interactive objid.
ENDMETHOD.

" ALV用户命令事件
METHOD sec_handle_user_command.
  PERFORM handle_user_commmand CHANGING e_ucomm objid.
ENDMETHOD.
```

## 7. 性能优化策略

### 7.1 数据库查询优化
```sql
-- 使用复合索引优化查询
CREATE INDEX idx_zreta002_composite ON zreta002(zht_id, zxyzt, zbegin);

-- 避免全表扫描
SELECT * FROM zreta002
WHERE zht_id = ? AND zxyzt IN ('I','P')
ORDER BY zcjrq DESC;
```

### 7.2 批量处理优化
```abap
" 使用FOR ALL ENTRIES优化关联查询
SELECT * FROM zreta002
  FOR ALL ENTRIES IN lt_zht_id
  WHERE zht_id = lt_zht_id-zht_id.

" 批量插入优化
INSERT zreta002 FROM TABLE lt_zreta002.
```

### 7.3 内存管理优化
```abap
" 及时清理大型内表
CLEAR: gt_excel[], gt_data[].
FREE: gt_excel, gt_data.

" 使用FIELD-SYMBOLS提高性能
FIELD-SYMBOLS: <ls_data> TYPE ty_data.
LOOP AT gt_data ASSIGNING <ls_data>.
  " 处理逻辑
ENDLOOP.
```

## 8. 错误处理机制

### 8.1 统一消息处理
```abap
" 消息收集结构
TYPES: BEGIN OF ty_message,
         msgty TYPE sy-msgty,
         msgv1 TYPE sy-msgv1,
         msgv2 TYPE sy-msgv2,
       END OF ty_message.

" 统一消息显示
CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
  EXPORTING
    title_text = '处理结果'
    message_list = lt_msglist[].
```

### 8.2 事务回滚机制
```abap
" 数据库事务控制
CALL FUNCTION 'DB_COMMIT'.
IF sy-subrc NE 0.
  CALL FUNCTION 'DB_ROLLBACK'.
  MESSAGE e003(zre) WITH '数据保存失败'.
ENDIF.
```

## 9. 安全性设计

### 9.1 数据加密
```abap
" 敏感数据加密存储
CALL FUNCTION 'SCMS_BASE64_ENCODE_STR'
  EXPORTING
    input = lv_sensitive_data
  IMPORTING
    output = lv_encoded_data.
```

### 9.2 操作日志记录
```abap
" 记录用户操作轨迹
ls_log-ztk_id = gs_ta02-ztk_id.
ls_log-operation = 'UPDATE'.
ls_log-user_id = sy-uname.
ls_log-timestamp = sy-datum && sy-uzeit.
INSERT zret_operation_log FROM ls_log.
```

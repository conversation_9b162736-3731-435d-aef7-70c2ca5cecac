# ZRED0040 返利合同管理系统 - 技术文档

## 1. 系统架构

### 1.1 程序结构
```
ZRED0040 (主程序)
├── ZRED0040_T01 (类型定义)
├── ZRED0040_T02 (商品组类型定义)
├── ZRED0040_C01 (类定义)
├── ZRED0040_S01 (选择屏幕)
├── ZRED0040_M01 (屏幕模块1)
├── ZRED0040_M02 (屏幕模块2)
├── ZRED0040_M03 (屏幕模块3)
├── ZRED0040_F01 (功能模块1)
├── ZRED0040_F02 (功能模块2)
└── ZRED0035_FPUB (公共功能)
```

### 1.2 事务代码
- **ZRED0040A**: 返利合同创建
- **ZRED0040B**: 返利合同修改  
- **ZRED0040C**: 返利合同显示

## 2. 数据库表结构

### 2.1 主表结构

#### 2.1.1 ZRETA001 - 返利合同主表
```sql
CREATE TABLE ZRETA001 (
  ZHT_ID      VARCHAR(20) NOT NULL,    -- 合同编号(主键)
  ZHTLX       VARCHAR(4),              -- 合同类型
  ZBUKRS      VARCHAR(4),              -- 公司代码
  ZHT_TXT     VARCHAR(40),             -- 合同描述
  ZHTID       VARCHAR(35),             -- 关联合同号
  ZBPTYPE     VARCHAR(1),              -- 伙伴类型(S/M)
  ZBPCODE     VARCHAR(10),             -- 伙伴代码
  EKGRP       VARCHAR(3),              -- 采购组
  ZHTYEAR     VARCHAR(4),              -- 签署年度
  ZBEGIN      DATE,                    -- 开始日期
  ZEND        DATE,                    -- 结束日期
  ZTMPID      VARCHAR(10),             -- 组织模板
  ZHSTYPE     VARCHAR(1),              -- 核算类型
  ZHSZQ       VARCHAR(3),              -- 核算周期
  ZJSZQ       VARCHAR(3),              -- 结算周期
  ZDFFS       VARCHAR(1),              -- 兑付方式
  ZFLZFF      VARCHAR(10),             -- 支付方
  ZPAYDAY     VARCHAR(2),              -- 付款期间
  ZLXR        VARCHAR(30),             -- 联系人
  ZLXFS       VARCHAR(30),             -- 联系方式
  ZCNYGHT_ID  VARCHAR(20),             -- 次年预估合同ID
  ZCNYGSG     VARCHAR(1),              -- 次年预估标识
  ZCJR        VARCHAR(12),             -- 创建人
  ZCJRQ       DATE,                    -- 创建日期
  ZCJSJ       TIME,                    -- 创建时间
  ZXGR        VARCHAR(12),             -- 修改人
  ZXGRQ       DATE,                    -- 修改日期
  ZXGSJ       TIME,                    -- 修改时间
  PRIMARY KEY (ZHT_ID)
);
```

#### 2.1.2 ZRETA002 - 返利条款表
```sql
CREATE TABLE ZRETA002 (
  ZTK_ID      VARCHAR(20) NOT NULL,    -- 条款编号(主键)
  ZHT_ID      VARCHAR(20),             -- 合同编号(外键)
  ZFLLX       VARCHAR(4),              -- 返利类型
  ZTKTYPE     VARCHAR(1),              -- 条款类型
  ZTK_TXT     VARCHAR(40),             -- 条款描述
  ZSPZ_ID     VARCHAR(20),             -- 商品组编号
  ZFLSPZ_ID   VARCHAR(20),             -- 返利商品组编号
  ZBEGIN      DATE,                    -- 开始日期
  ZEND        DATE,                    -- 结束日期
  ZTMPID      VARCHAR(10),             -- 组织模板
  ZXYZT       VARCHAR(1),              -- 协议状态
  ZCLRID      VARCHAR(10),             -- 计算规则ID
  ZLEIB       VARCHAR(1),              -- 类别
  ZXYBSTYP    VARCHAR(1),              -- 协议类型
  ZCJR        VARCHAR(12),             -- 创建人
  ZCJRQ       DATE,                    -- 创建日期
  ZCJSJ       TIME,                    -- 创建时间
  PRIMARY KEY (ZTK_ID),
  FOREIGN KEY (ZHT_ID) REFERENCES ZRETA001(ZHT_ID)
);
```

#### 2.1.3 ZRET0009 - 商品组主表
```sql
CREATE TABLE ZRET0009 (
  ZSPZ_ID     VARCHAR(20) NOT NULL,    -- 商品组编号(主键)
  ZHT_ID      VARCHAR(20),             -- 合同编号(外键)
  ZSPZID_TXT  VARCHAR(40),             -- 商品组描述
  ZUSAGE      VARCHAR(1),              -- 用途(P=促销)
  ZBPCODE     VARCHAR(10),             -- 业务伙伴代码
  ZCJR        VARCHAR(12),             -- 创建人
  ZCJRQ       DATE,                    -- 创建日期
  ZCJSJ       TIME,                    -- 创建时间
  ZXGR        VARCHAR(12),             -- 修改人
  ZXGRQ       DATE,                    -- 修改日期
  ZXGSJ       TIME,                    -- 修改时间
  PRIMARY KEY (ZSPZ_ID),
  FOREIGN KEY (ZHT_ID) REFERENCES ZRETA001(ZHT_ID)
);
```

#### 2.1.4 ZRET0020 - 商品组明细表
```sql
CREATE TABLE ZRET0020 (
  ZSPZ_ID     VARCHAR(20) NOT NULL,    -- 商品组编号(主键)
  MATNR       VARCHAR(18) NOT NULL,    -- 物料编号(主键)
  ZMGROUP     VARCHAR(10) NOT NULL,    -- 物料组(主键)
  ZHT_ID      VARCHAR(20),             -- 合同编号
  ZACPR       DECIMAL(15,2),           -- 核算价格
  ZPEINH      DECIMAL(5,0),            -- 价格倍数
  ZPEINH_Q    DECIMAL(5,0),            -- 数量倍数
  ZSPBC       VARCHAR(1),              -- 排除标识
  ZBUY        DECIMAL(13,3),           -- 买数量
  ZFREE       DECIMAL(13,3),           -- 赠数量
  ZCMPST      DECIMAL(15,2),           -- 加提单价
  PRIMARY KEY (ZSPZ_ID, MATNR, ZMGROUP),
  FOREIGN KEY (ZSPZ_ID) REFERENCES ZRET0009(ZSPZ_ID)
);
```

#### 2.1.5 ZRET0020_ITEM - 分段核算价表
```sql
CREATE TABLE ZRET0020_ITEM (
  ZSPZ_ID     VARCHAR(20) NOT NULL,    -- 商品组编号(主键)
  MATNR       VARCHAR(18) NOT NULL,    -- 物料编号(主键)
  ZMGROUP     VARCHAR(10) NOT NULL,    -- 物料组(主键)
  ZBEGDT      DATE NOT NULL,           -- 开始日期(主键)
  ZENDDT      DATE,                    -- 结束日期
  ZACPR       DECIMAL(15,2),           -- 核算价格
  PRIMARY KEY (ZSPZ_ID, MATNR, ZMGROUP, ZBEGDT),
  FOREIGN KEY (ZSPZ_ID, MATNR, ZMGROUP) REFERENCES ZRET0020(ZSPZ_ID, MATNR, ZMGROUP)
);
```

### 2.2 配置表结构

#### 2.2.1 ZRETC001 - 组织模板配置表
```sql
CREATE TABLE ZRETC001 (
  ZTMPID      VARCHAR(10) NOT NULL,    -- 模板编号(主键)
  ZHTLX       VARCHAR(4) NOT NULL,     -- 合同类型(主键)
  ZBUKRS      VARCHAR(4),              -- 公司代码
  ZTMPTXT     VARCHAR(40),             -- 模板描述
  ZTKTYPE     VARCHAR(1),              -- 条款类型
  ZTMPTY      VARCHAR(1),              -- 模板类型
  PRIMARY KEY (ZTMPID, ZHTLX)
);
```

#### 2.2.2 ZRETC009 - 协议类型配置表
```sql
CREATE TABLE ZRETC009 (
  ZXYBSTYP    VARCHAR(1) NOT NULL,     -- 协议类型(主键)
  ZFLLX       VARCHAR(4) NOT NULL,     -- 返利类型(主键)
  PRIMARY KEY (ZXYBSTYP, ZFLLX)
);
```

## 3. 核心算法

### 3.1 返利金额计算算法

#### 3.1.1 计算类条款算法
```abap
FORM frm_get_anbtr CHANGING ps_tk_js TYPE LINE OF tt_tk.
  " 获取核算基准配置
  SELECT SINGLE * INTO @DATA(ls_s03) 
    FROM zret0003 WHERE zhsjz = @ps_tk_js-zhsjz.
  
  " 获取业务数据汇总
  SELECT SUM(a~zpurysje) AS zpurysje,
         SUM(a~zdistysje) AS zdistysje,
         SUM(a~zsaleysje) AS zsaleysje,
         SUM(a~zduercv) AS zduercv,
         SUM(a~zactlrcv) AS zactlrcv
    FROM zret0017 AS a
    WHERE zxy_id = @ps_tk_js-ztk_id
    GROUP BY a~zxy_id
    INTO TABLE @DATA(lt_t17_sum).
  
  " 根据核算基准计算返利金额
  IF ls_s03-zlower = 'X'.
    " 取最小值
    ps_tk_js-anbtr = nmin(
      val1 = ls_t17_sum-zpurysje
      val2 = ls_t17_sum-zdistysje
      val3 = ls_t17_sum-zsaleysje
      val4 = ls_t17_sum-zactlrcv
    ).
  ELSEIF ls_s03-zhigher = 'X'.
    " 取最大值
    ps_tk_js-anbtr = nmax(
      val1 = ls_t17_sum-zpurysje
      val2 = ls_t17_sum-zdistysje
      val3 = ls_t17_sum-zsaleysje
      val4 = ls_t17_sum-zactlrcv
    ).
  ENDIF.
ENDFORM.
```

### 3.2 数据验证算法

#### 3.2.1 合同重复性检查
```abap
FORM frm_check_double USING ps_ta01 TYPE LINE OF tt_ta01
                      CHANGING pt_msglist TYPE scp1_general_errors.
  " 检查同一伙伴在同一时间段是否存在重复合同
  SELECT SINGLE zht_id FROM zreta001 
    WHERE zbukrs = @ps_ta01-zbukrs
      AND zbptype = @ps_ta01-zbptype
      AND zbpcode = @ps_ta01-zbpcode
      AND zht_id NE @ps_ta01-zht_id
      AND zhtlx EQ @ps_ta01-zhtlx
      AND ((zbegin <= @ps_ta01-zbegin AND zend >= @ps_ta01-zbegin) OR
           (zbegin >= @ps_ta01-zbegin AND zbegin <= @ps_ta01-zend))
    INTO @DATA(lv_tmp_zht_id).
  
  IF sy-subrc EQ 0.
    " 存在重复合同
    APPEND VALUE #(msgv1 = '日期范围存在交叉! 合同号：' && lv_tmp_zht_id) 
           TO pt_msglist.
  ENDIF.
ENDFORM.
```

## 4. 屏幕设计

### 4.1 选择屏幕 (1000)
```abap
SELECTION-SCREEN BEGIN OF BLOCK b01 WITH FRAME TITLE TEXT-001.
PARAMETERS:
  p_zhtlx  TYPE zretc001-zhtlx AS LISTBOX VISIBLE LENGTH 20,
  p_zbukrs TYPE zretc001-zbukrs MATCHCODE OBJECT c_t001.
PARAMETERS:
  p_zht_id TYPE zreta001-zht_id MATCHCODE OBJECT zresh0016.
SELECTION-SCREEN END OF BLOCK b01.

SELECTION-SCREEN BEGIN OF BLOCK b02 WITH FRAME TITLE TEXT-002.
PARAMETERS:
  rb_add  TYPE char1 RADIOBUTTON GROUP g1,
  rb_edit TYPE char1 RADIOBUTTON GROUP g1,
  rb_dis  TYPE char1 RADIOBUTTON GROUP g1.
SELECTION-SCREEN END OF BLOCK b02.
```

### 4.2 主屏幕 (2000)
- **标签页控件**: TAG_TA01 (合同信息/商品组/返利条款)
- **子屏幕**: 3001, 3002, 3003
- **表格控件**: TC_T09 (商品组列表)

### 4.3 商品组维护屏幕 (4100)
- **表格控件**: TC_SPZ (商品明细)
- **分段核算价屏幕**: 4101

## 5. 权限对象

### 5.1 ZREAR009 - 返利合同权限
```
字段1: BUKRS (公司代码)
字段2: ACTVT (活动类型: 01=创建, 02=修改, 03=显示)
```

### 5.2 权限检查代码
```abap
AUTHORITY-CHECK OBJECT 'ZREAR009'
  ID 'BUKRS' FIELD ls_t006-zbukrs
  ID 'ACTVT' FIELD '03'.
IF sy-subrc NE 0.
  " 无权限处理
ENDIF.
```

## 6. 接口设计

### 6.1 调用其他程序
```abap
" 调用商品组维护程序
SUBMIT zrem0002
  WITH rb_add = 'X'
  WITH p_zht_id = gs_ta01-zht_id
  AND RETURN.

" 调用条款维护程序  
SUBMIT zred0041
  WITH p_zfllx = gs_tk_call-zfllx
  WITH p_zxybtp = gs_tk_call-zxybstyp
  WITH p_zht_id = gs_ta01-zht_id
  AND RETURN.
```

### 6.2 内存参数传递
```abap
" 设置内存参数
SET PARAMETER ID 'ZHT_ID' FIELD gs_ta01-zht_id.
SET PARAMETER ID 'ZSPZ_ID' FIELD gs_t09_sub-zspz_id.

" 获取内存参数
GET PARAMETER ID 'ZHT_ID' FIELD p_zht_id.
```

## 7. 性能优化

### 7.1 数据库查询优化
- 使用索引字段进行查询
- 避免SELECT * 语句
- 使用FOR ALL ENTRIES优化关联查询
- 合理使用缓冲表

### 7.2 内存管理
- 及时清理大型内表
- 使用FIELD-SYMBOLS提高性能
- 避免深层嵌套循环

## 8. 错误处理

### 8.1 消息处理
```abap
" 统一消息处理
CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
  EXPORTING
    title_text = '消息提示'
    message_list = lt_msglist[].
```

### 8.2 异常处理
```abap
TRY.
  " 业务逻辑
CATCH cx_sy_conversion_no_number INTO DATA(lv_error).
  MESSAGE lv_error->get_text() TYPE 'E'.
ENDTRY.
```

## 9. 业务流程时序图

### 9.1 合同创建流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 用户界面
    participant Logic as 业务逻辑
    participant Auth as 权限检查
    participant DB as 数据库
    participant Workflow as 工作流

    User->>UI: 选择创建合同
    UI->>Logic: 初始化创建模式
    Logic->>Auth: 检查创建权限
    Auth-->>Logic: 权限验证结果

    alt 权限验证通过
        Logic->>UI: 显示合同创建界面
        User->>UI: 填写合同基本信息
        UI->>Logic: 验证输入数据
        Logic->>DB: 检查数据重复性
        DB-->>Logic: 返回检查结果

        alt 数据验证通过
            User->>UI: 保存合同
            UI->>Logic: 执行保存操作
            Logic->>DB: 生成合同编号
            DB-->>Logic: 返回合同编号
            Logic->>DB: 保存合同数据
            DB-->>Logic: 保存成功
            Logic->>Workflow: 触发审批流程(可选)
            Workflow-->>Logic: 流程启动成功
            Logic->>UI: 显示保存成功消息
            UI->>User: 提示操作完成
        else 数据验证失败
            Logic->>UI: 显示错误消息
            UI->>User: 提示修正数据
        end
    else 权限验证失败
        Logic->>UI: 显示权限错误
        UI->>User: 提示无权限操作
    end
```

### 9.2 商品组维护流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Main as 主程序
    participant SPZ as 商品组程序
    participant Excel as Excel处理
    participant DB as 数据库

    User->>Main: 点击新增商品组
    Main->>SPZ: 调用ZREM0002程序
    SPZ->>DB: 查询可用商品列表
    DB-->>SPZ: 返回商品数据
    SPZ->>User: 显示商品选择界面

    User->>SPZ: 选择商品或导入Excel
    alt Excel导入
        SPZ->>Excel: 读取剪贴板数据
        Excel-->>SPZ: 返回Excel数据
        SPZ->>SPZ: 验证数据格式
        alt 格式正确
            SPZ->>SPZ: 转换为内表数据
        else 格式错误
            SPZ->>User: 显示格式错误消息
        end
    else 手工选择
        User->>SPZ: 勾选商品复选框
        SPZ->>SPZ: 更新选择状态
    end

    User->>SPZ: 保存商品组
    SPZ->>DB: 验证商品组数据
    DB-->>SPZ: 返回验证结果
    alt 验证通过
        SPZ->>DB: 保存商品组数据
        DB-->>SPZ: 保存成功
        SPZ->>Main: 返回主程序
        Main->>DB: 刷新商品组列表
        DB-->>Main: 返回最新数据
        Main->>User: 显示更新后列表
    else 验证失败
        SPZ->>User: 显示验证错误
    end
```

### 9.3 返利条款配置流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Main as 主程序(ZRED0040)
    participant TK as 条款程序(ZRED0041)
    participant Config as 配置管理
    participant Calc as 计算引擎
    participant DB as 数据库

    User->>Main: 点击添加条款
    Main->>Main: 显示条款类型选择
    User->>Main: 选择条款参数
    Main->>TK: 调用条款维护程序

    TK->>Config: 获取模板配置
    Config-->>TK: 返回模板信息
    TK->>DB: 查询商品组数据
    DB-->>TK: 返回商品组列表
    TK->>User: 显示条款配置界面

    User->>TK: 配置条款参数
    TK->>TK: 验证参数有效性
    alt 参数验证通过
        User->>TK: 保存条款
        TK->>Calc: 验证计算规则
        Calc-->>TK: 返回验证结果
        alt 规则验证通过
            TK->>DB: 保存条款数据
            DB-->>TK: 保存成功
            TK->>Main: 返回主程序
            Main->>DB: 刷新条款列表
            DB-->>Main: 返回最新条款
            Main->>User: 显示更新后条款
        else 规则验证失败
            TK->>User: 显示规则错误
        end
    else 参数验证失败
        TK->>User: 显示参数错误
    end
```

### 9.4 数据查询和显示流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 界面层
    participant Cache as 缓存层
    participant Logic as 业务逻辑层
    participant DB as 数据库层
    participant Auth as 权限层

    User->>UI: 输入查询条件
    UI->>Logic: 发起数据查询请求
    Logic->>Auth: 检查查询权限
    Auth-->>Logic: 返回权限结果

    alt 有查询权限
        Logic->>Cache: 检查缓存数据
        alt 缓存命中
            Cache-->>Logic: 返回缓存数据
        else 缓存未命中
            Logic->>DB: 查询合同主数据
            DB-->>Logic: 返回合同数据
            Logic->>DB: 查询商品组数据
            DB-->>Logic: 返回商品组数据
            Logic->>DB: 查询条款数据
            DB-->>Logic: 返回条款数据
            Logic->>Cache: 更新缓存
        end

        Logic->>Logic: 数据权限过滤
        Logic->>Logic: 计算返利金额
        Logic->>UI: 返回处理后数据
        UI->>User: 显示查询结果

        User->>UI: 点击条款详情
        UI->>Logic: 获取条款明细
        Logic->>DB: 查询条款详细信息
        DB-->>Logic: 返回详细数据
        Logic->>UI: 返回条款详情
        UI->>User: 显示条款详情页面
    else 无查询权限
        Logic->>UI: 返回权限错误
        UI->>User: 显示无权限提示
    end
```

## 10. 部署和配置

### 10.1 传输请求配置
```
开发系统: DEV
测试系统: QAS
生产系统: PRD

传输路径: DEV -> QAS -> PRD
```

### 10.2 权限角色配置
```
角色: Z_RET_CONTRACT_CREATE (合同创建)
角色: Z_RET_CONTRACT_CHANGE (合同修改)
角色: Z_RET_CONTRACT_DISPLAY (合同显示)
```

### 10.3 变式配置
```
变式: Z_RET_DEFAULT (默认变式)
- 默认合同类型
- 默认公司代码
- 默认操作模式
```

# ZRED0041条款管理 & ZRED0040合同管理系统 - 业务流程时序图

## 概述

本文档包含了返利平台管理系统的核心业务流程时序图，详细展示了合同管理和条款管理的各个关键流程的交互逻辑和处理步骤。

## 1. 合同创建流程时序图

### 流程说明
合同创建流程展示了ZRED0040系统中从用户选择创建模式到合同成功保存的完整过程，包括权限检查、数据验证、商品组配置等关键环节。

### 关键参与者
- **用户**: 合同创建操作员
- **ZRED0040界面**: 合同管理用户界面
- **权限检查**: 基于ZREAR009权限对象的检查模块
- **数据验证**: 数据完整性和业务规则验证
- **数据库**: 合同数据存储
- **商品组管理**: ZREM0002商品组维护程序

### 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as ZRED0040界面
    participant Auth as 权限检查
    participant Valid as 数据验证
    participant DB as 数据库
    participant SPZ as 商品组管理

    User->>UI: 选择创建模式(ZRED0040A)
    UI->>Auth: 检查创建权限(ZREAR009)
    Auth-->>UI: 权限验证结果

    alt 权限验证通过
        UI->>User: 显示合同创建界面
        User->>UI: 填写合同基本信息
        Note over User,UI: 合同类型、公司代码、伙伴信息等

        User->>UI: 配置商品组
        UI->>SPZ: 调用商品组维护(ZREM0002)
        SPZ->>DB: 保存商品组数据(ZRET0009/0020)
        SPZ-->>UI: 返回商品组ID

        User->>UI: 保存合同
        UI->>Valid: 数据完整性验证
        Valid->>Valid: 检查必填字段
        Valid->>Valid: 检查日期逻辑
        Valid->>Valid: 检查业务规则

        alt 验证通过
            Valid->>DB: 生成合同编号
            DB-->>Valid: 返回合同编号
            Valid->>DB: 保存合同数据(ZRETA001)
            DB-->>Valid: 保存成功
            Valid->>UI: 返回成功消息
            UI->>User: 显示保存成功
        else 验证失败
            Valid->>UI: 返回错误消息
            UI->>User: 显示错误信息
        end
    else 权限验证失败
        Auth->>UI: 返回权限错误
        UI->>User: 显示无权限提示
    end
```

## 2. 条款创建流程时序图

### 流程说明
条款创建流程展示了ZRED0041系统中条款从创建到保存的完整过程，包括参考创建模式、计算规则配置、商品组关联等核心功能。

### 关键参与者
- **用户**: 条款创建操作员
- **ZRED0041界面**: 条款管理用户界面
- **权限检查**: 条款操作权限验证
- **数据验证**: 条款数据验证模块
- **数据库**: 条款和关联数据存储
- **计算规则**: ZRED0038规则维护程序
- **合同管理**: ZRED0040合同信息获取

### 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as ZRED0041界面
    participant Auth as 权限检查
    participant Valid as 数据验证
    participant DB as 数据库
    participant Rule as 计算规则
    participant Contract as 合同管理

    User->>UI: 选择条款创建(ZRED0041A)
    UI->>Auth: 检查创建权限
    Auth-->>UI: 权限验证结果

    alt 权限验证通过
        UI->>User: 显示条款创建界面
        User->>UI: 选择返利类型和协议类型
        UI->>Contract: 获取合同信息
        Contract->>DB: 查询合同数据(ZRETA001)
        DB-->>Contract: 返回合同信息
        Contract-->>UI: 合同信息

        User->>UI: 填写条款基本信息
        Note over User,UI: 条款描述、生效期间、支付方式等

        alt 参考创建模式
            User->>UI: 选择参考条款
            UI->>DB: 查询参考条款数据
            DB-->>UI: 返回参考数据
            UI->>UI: 复制参考数据到新条款
        end

        User->>UI: 配置计算规则
        UI->>Rule: 调用规则维护(ZRED0038)
        Rule->>DB: 保存规则数据(ZRETC005)
        Rule-->>UI: 返回规则ID

        User->>UI: 关联商品组
        UI->>DB: 查询可用商品组
        DB-->>UI: 返回商品组列表

        User->>UI: 保存条款
        UI->>Valid: 条款数据验证
        Valid->>Valid: 检查必填字段
        Valid->>Valid: 检查日期范围
        Valid->>Valid: 检查业务规则
        Valid->>Valid: 检查权限范围

        alt 验证通过
            Valid->>DB: 生成条款编号
            DB-->>Valid: 返回条款编号
            Valid->>DB: 保存条款数据(ZRETA002)
            Valid->>DB: 保存关联数据(ZRET0006)
            DB-->>Valid: 保存成功
            Valid->>UI: 返回成功消息
            UI->>User: 显示保存成功
        else 验证失败
            Valid->>UI: 返回错误消息
            UI->>User: 显示错误信息
        end
    else 权限验证失败
        Auth->>UI: 返回权限错误
        UI->>User: 显示无权限提示
    end
```

## 3. 条款审批流程时序图

### 流程说明
条款审批流程展示了ZRED0041D审批模式下，从审批人进入系统到完成审批操作的全过程，包括锁定机制、业务验证、状态变更、日志记录等关键环节。

### 关键参与者
- **审批人**: 具有审批权限的用户
- **ZRED0041界面**: 条款审批用户界面
- **权限检查**: 审批权限验证模块
- **锁定检查**: 条款锁定状态管理
- **业务验证**: 审批前业务规则验证
- **数据库**: 条款状态和数据存储
- **日志记录**: 审批操作日志记录
- **消息通知**: 审批结果通知机制

### 时序图

```mermaid
sequenceDiagram
    participant User as 审批人
    participant UI as ZRED0041界面
    participant Auth as 权限检查
    participant Lock as 锁定检查
    participant Valid as 业务验证
    participant DB as 数据库
    participant Log as 日志记录
    participant Notify as 消息通知

    User->>UI: 进入审批模式(ZRED0041D)
    UI->>Auth: 检查审批权限
    Auth-->>UI: 权限验证结果

    alt 权限验证通过
        UI->>DB: 查询待审批条款
        DB-->>UI: 返回条款列表
        UI->>User: 显示待审批条款

        User->>UI: 选择条款进行审批
        UI->>DB: 查询条款详细信息
        DB-->>UI: 返回条款数据

        UI->>Lock: 检查条款锁定状态
        Lock->>DB: 查询锁定信息
        DB-->>Lock: 返回锁定状态

        alt 条款未锁定
            Lock->>DB: 设置编辑锁定
            DB-->>Lock: 锁定成功

            UI->>User: 显示条款详情
            User->>UI: 执行审批操作(通过/拒绝)

            UI->>Valid: 审批前业务验证
            Valid->>Valid: 检查月结锁定期间
            Valid->>Valid: 检查次年预估标识
            Valid->>Valid: 检查条款状态
            Valid->>Valid: 检查关联数据完整性

            alt 验证通过
                Valid->>DB: 更新条款状态
                Note over Valid,DB: 更新ZXYZT和FRGC1字段
                DB-->>Valid: 更新成功

                Valid->>Log: 记录状态变更日志
                Log->>DB: 保存日志数据(ZRET0050)
                DB-->>Log: 日志保存成功

                Valid->>Notify: 发送审批结果通知
                Notify->>Notify: 生成消息内容
                Notify-->>Valid: 通知发送成功

                Valid->>Lock: 释放编辑锁定
                Lock->>DB: 清除锁定标识
                DB-->>Lock: 锁定释放成功

                Valid->>UI: 返回审批成功消息
                UI->>User: 显示审批完成

            else 验证失败
                Valid->>Lock: 释放编辑锁定
                Lock->>DB: 清除锁定标识
                Valid->>UI: 返回验证错误
                UI->>User: 显示错误信息
            end

        else 条款已锁定
            Lock->>UI: 返回锁定错误
            UI->>User: 显示条款被锁定提示
        end

    else 权限验证失败
        Auth->>UI: 返回权限错误
        UI->>User: 显示无审批权限提示
    end
```

## 4. Excel批量导入流程时序图

### 流程说明
Excel批量导入流程展示了ZRED0041系统中批量处理功能的完整过程，从模板下载到数据导入完成，包括文件解析、数据验证、权限检查、批量处理等关键步骤。

### 关键参与者
- **用户**: 批量导入操作员
- **ZRED0041界面**: 批量导入用户界面
- **文件处理**: Excel文件解析模块
- **数据验证**: 批量数据验证模块
- **权限检查**: 批量操作权限验证
- **数据库**: 批量数据存储
- **错误日志**: 导入错误记录和处理

### 导入模式
- **ZRED0004**: 固定条款批量导入-创建模板
- **ZRED0005**: 固定条款批量导入-新增协议模板
- **ZRED0006**: 固定协议批量调整导入模板
- **ZRED0007**: 条款批量修改模板

### 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as ZRED0041界面
    participant File as 文件处理
    participant Valid as 数据验证
    participant Auth as 权限检查
    participant DB as 数据库
    participant Log as 错误日志

    User->>UI: 选择批量导入功能
    UI->>User: 显示导入模式选择
    Note over User,UI: 创建/新增协议/修改/调整

    User->>UI: 选择导入模式
    UI->>UI: 下载对应Excel模板
    Note over UI: ZRED0004/0005/0006/0007模板

    User->>User: 填写Excel数据
    User->>UI: 上传Excel文件

    UI->>File: 读取Excel文件
    File->>File: 解析Excel数据
    File->>File: 转换为内部数据格式
    File-->>UI: 返回解析结果

    alt 文件解析成功
        UI->>Valid: 开始数据验证

        loop 逐行验证数据
            Valid->>Valid: 检查必填字段
            Valid->>Valid: 检查数据格式
            Valid->>Valid: 检查域值有效性
            Valid->>DB: 检查主数据存在性
            DB-->>Valid: 返回检查结果
            Valid->>Valid: 检查业务规则

            alt 行数据验证失败
                Valid->>Log: 记录错误信息
                Log->>Log: 添加到错误列表
            end
        end

        Valid->>Auth: 批量权限检查
        Auth->>Auth: 检查公司代码权限
        Auth->>Auth: 检查操作权限
        Auth-->>Valid: 返回权限检查结果

        alt 存在验证错误
            Valid->>UI: 返回错误列表
            UI->>User: 显示错误信息和ALV
            User->>User: 修正错误数据
            User->>UI: 重新上传文件
        else 验证全部通过
            Valid->>DB: 开始批量数据处理

            loop 逐行处理数据
                DB->>DB: 生成业务编号
                DB->>DB: 保存条款数据
                DB->>DB: 保存关联数据

                alt 保存成功
                    DB->>Log: 记录成功信息
                else 保存失败
                    DB->>Log: 记录失败信息
                    DB->>DB: 回滚当前行数据
                end
            end

            DB-->>Valid: 返回处理结果
            Valid->>UI: 返回导入结果
            UI->>User: 显示导入成功/失败统计
        end

    else 文件解析失败
        File->>UI: 返回解析错误
        UI->>User: 显示文件格式错误
    end
```

## 5. 流程总结

### 5.1 流程特点
1. **权限优先**: 所有流程都以权限检查为第一步，确保操作安全性
2. **数据验证**: 多层次的数据验证机制，包括格式、业务规则、存在性检查
3. **锁定机制**: 审批流程中的锁定机制防止并发操作冲突
4. **错误处理**: 完善的错误处理和回滚机制，确保数据一致性
5. **日志记录**: 关键操作的完整日志记录，便于审计和追溯

### 5.2 关键控制点
1. **权限控制**: 基于ZREAR009权限对象的多级权限验证
2. **状态管理**: 条款状态的严格控制和转换规则
3. **月结锁定**: 月结期间的数据保护机制
4. **批量处理**: 大数据量处理的性能优化和错误处理

### 5.3 系统集成
1. **程序调用**: ZRED0040与ZRED0041之间的无缝集成
2. **数据共享**: 通过内存参数和数据库实现数据共享
3. **模块复用**: 共用模块的设计提高了系统的可维护性

### 5.4 用户体验
1. **操作引导**: 清晰的操作流程和用户提示
2. **错误反馈**: 详细的错误信息和修正建议
3. **批量处理**: 支持大批量数据的高效处理
4. **状态可视**: 实时的处理状态和进度反馈

## 6. 技术要点

### 6.1 并发控制
- 使用数据库锁定机制防止并发编辑
- 状态检查确保操作的原子性
- 事务回滚保证数据一致性

### 6.2 性能优化
- 批量数据处理的分批处理策略
- 数据库查询的索引优化
- 内存管理和资源释放

### 6.3 安全机制
- 多层次的权限验证
- 操作日志的完整记录
- 敏感数据的保护措施

### 6.4 扩展性设计
- 模块化的程序结构
- 可配置的业务规则
- 标准化的接口设计

---

**文档说明**: 本文档包含了返利平台管理系统的四个核心业务流程时序图，详细展示了系统的交互逻辑和处理机制。这些时序图可以帮助开发人员理解系统架构，协助业务人员掌握操作流程，为系统维护和优化提供重要参考。
